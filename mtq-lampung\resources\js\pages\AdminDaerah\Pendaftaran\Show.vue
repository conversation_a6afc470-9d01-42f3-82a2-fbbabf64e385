<template>
  <AppLayout title="Detail Pendaftaran">
    <template #header>
      <Heading>Detail Pendaftaran</Heading>
    </template>

    <div class="max-w-4xl mx-auto space-y-6">
      <!-- Pendaftaran Info -->
      <Card>
        <CardHeader>
          <CardTitle>Informasi Pendaftaran</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-6">
            <div>
              <Label class="text-sm font-medium text-gray-500">Nomor Urut</Label>
              <p class="text-lg font-semibold">{{ pendaftaran.nomor_urut }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Status Pendaftaran</Label>
              <Badge :variant="getStatusVariant(pendaftaran.status_pendaftaran)" class="mt-1">
                {{ getStatusLabel(pendaftaran.status_pendaftaran) }}
              </Badge>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Biaya Pendaftaran</Label>
              <p class="text-lg font-semibold text-green-600">
                Rp {{ formatCurrency(pendaftaran.biaya_pendaftaran) }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Tanggal Daftar</Label>
              <p class="text-lg">{{ formatDate(pendaftaran.created_at) }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Peserta Info -->
      <Card>
        <CardHeader>
          <CardTitle>Informasi Peserta</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-6">
            <div>
              <Label class="text-sm font-medium text-gray-500">Nama Lengkap</Label>
              <p class="text-lg font-semibold">{{ pendaftaran.peserta.nama_lengkap }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">NIK</Label>
              <p class="text-lg">{{ pendaftaran.peserta.nik }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Email</Label>
              <p class="text-lg">{{ pendaftaran.peserta.user?.email }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">No. Telepon</Label>
              <p class="text-lg">{{ pendaftaran.peserta.no_telepon || '-' }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Tempat, Tanggal Lahir</Label>
              <p class="text-lg">
                {{ pendaftaran.peserta.tempat_lahir }}, {{ formatDate(pendaftaran.peserta.tanggal_lahir) }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Jenis Kelamin</Label>
              <p class="text-lg">{{ pendaftaran.peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
            </div>
            <div class="col-span-2">
              <Label class="text-sm font-medium text-gray-500">Alamat</Label>
              <p class="text-lg">{{ pendaftaran.peserta.alamat }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Golongan Info -->
      <Card>
        <CardHeader>
          <CardTitle>Informasi Golongan</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-6">
            <div>
              <Label class="text-sm font-medium text-gray-500">Cabang Lomba</Label>
              <p class="text-lg font-semibold">{{ pendaftaran.golongan.cabang_lomba?.nama_cabang }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Nama Golongan</Label>
              <p class="text-lg">{{ pendaftaran.golongan.nama_golongan }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Jenis Kelamin</Label>
              <p class="text-lg">{{ pendaftaran.golongan.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Batas Umur</Label>
              <p class="text-lg">{{ pendaftaran.golongan.batas_umur_min }} - {{ pendaftaran.golongan.batas_umur_max }} tahun</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Pembayaran Info -->
      <Card v-if="pendaftaran.pembayaran">
        <CardHeader>
          <CardTitle>Informasi Pembayaran</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-2 gap-6">
            <div>
              <Label class="text-sm font-medium text-gray-500">Status Pembayaran</Label>
              <Badge :variant="getPaymentVariant(pendaftaran.pembayaran.status_pembayaran)" class="mt-1">
                {{ getPaymentLabel(pendaftaran.pembayaran.status_pembayaran) }}
              </Badge>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Jumlah Bayar</Label>
              <p class="text-lg font-semibold text-green-600">
                Rp {{ formatCurrency(pendaftaran.pembayaran.jumlah_bayar) }}
              </p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Metode Pembayaran</Label>
              <p class="text-lg capitalize">{{ pendaftaran.pembayaran.metode_pembayaran }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Tanggal Bayar</Label>
              <p class="text-lg">{{ formatDate(pendaftaran.pembayaran.tanggal_bayar) }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Documents -->
      <Card v-if="pendaftaran.dokumen_peserta && pendaftaran.dokumen_peserta.length > 0">
        <CardHeader>
          <CardTitle>Dokumen Peserta</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3">
            <div
              v-for="doc in pendaftaran.dokumen_peserta"
              :key="doc.id_dokumen"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <Icon name="file-text" class="w-5 h-5 text-gray-400" />
                <div>
                  <p class="font-medium">{{ doc.jenis_dokumen }}</p>
                  <p class="text-sm text-gray-500">{{ doc.nama_file }}</p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <Badge :variant="getDocumentVariant(doc.status_verifikasi)">
                  {{ getDocumentLabel(doc.status_verifikasi) }}
                </Badge>
                <Button variant="outline" size="sm" @click="downloadDocument(doc.file_path)">
                  <Icon name="download" class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <Card>
        <CardContent class="p-6">
          <div class="flex flex-wrap gap-4">
            <Button 
              as="link" 
              :href="route('admin-daerah.pendaftaran.edit', pendaftaran.id_pendaftaran)"
            >
              <Icon name="edit" class="w-4 h-4 mr-2" />
              Edit Pendaftaran
            </Button>
            
            <Button 
              as="link" 
              :href="route('admin-daerah.pendaftaran.documents', pendaftaran.id_pendaftaran)"
              variant="outline"
            >
              <Icon name="file-text" class="w-4 h-4 mr-2" />
              Kelola Dokumen
            </Button>
            
            <Button 
              variant="outline"
              @click="window.print()"
            >
              <Icon name="printer" class="w-4 h-4 mr-2" />
              Cetak Data
            </Button>

            <Button 
              variant="outline"
              @click="$inertia.visit(route('admin-daerah.pendaftaran.index'))"
            >
              <Icon name="arrow-left" class="w-4 h-4 mr-2" />
              Kembali
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'

interface Props {
  pendaftaran: any
}

const props = defineProps<Props>()

const getStatusVariant = (status: string) => {
  const variants = {
    'draft': 'secondary',
    'submitted': 'default',
    'verified': 'default',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels = {
    'draft': 'Draft',
    'submitted': 'Disubmit',
    'verified': 'Diverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const getPaymentVariant = (status: string) => {
  const variants = {
    'pending': 'secondary',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getPaymentLabel = (status: string) => {
  const labels = {
    'pending': 'Pending',
    'approved': 'Lunas',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const getDocumentVariant = (status: string) => {
  const variants = {
    'pending': 'secondary',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getDocumentLabel = (status: string) => {
  const labels = {
    'pending': 'Pending',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID').format(amount)
}

const downloadDocument = (filePath: string) => {
  window.open(`/storage/${filePath}`, '_blank')
}
</script>
