<template>
  <AppLayout title="Kelola Pendaftaran">
    <template #header>
      <Heading>Kelola Pendaftaran</Heading>
    </template>

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama peserta, NIK..."
                @input="search"
              />
            </div>
            <div>
              <Label for="status">Status Pendaftaran</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="submitted">Disubmit</SelectItem>
                  <SelectItem value="verified">Diverifikasi</SelectItem>
                  <SelectItem value="approved">Disetujui</SelectItem>
                  <SelectItem value="rejected">Di<PERSON>lak</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label for="golongan">Golongan</Label>
              <Select v-model="filters.golongan" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Golongan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Golongan</SelectItem>
                  <SelectItem v-for="g in golongan" :key="g.id_golongan" :value="g.id_golongan.toString()">
                    {{ g.nama_golongan }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="resetFilters" variant="outline" class="w-full">
                Reset Filter
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <Badge variant="secondary">
            Total: {{ pendaftaran.total }} pendaftaran
          </Badge>
        </div>
        <Button as="link" :href="route('admin-daerah.pendaftaran.create')">
          <Icon name="plus" class="w-4 h-4 mr-2" />
          Daftarkan Peserta
        </Button>
      </div>

      <!-- Pendaftaran Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Golongan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nomor Urut
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pembayaran
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tanggal
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in pendaftaran.data" :key="item.id_pendaftaran" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <Avatar class="h-10 w-10">
                        <AvatarFallback>
                          {{ getInitials(item.peserta.nama_lengkap) }}
                        </AvatarFallback>
                      </Avatar>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          {{ item.peserta.nama_lengkap }}
                        </div>
                        <div class="text-sm text-gray-500">
                          {{ item.peserta.nik }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {{ item.golongan.cabang_lomba?.nama_cabang }}
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ item.golongan.nama_golongan }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ item.nomor_urut }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(item.status_pendaftaran)">
                      {{ getStatusLabel(item.status_pendaftaran) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge v-if="item.pembayaran" :variant="getPaymentVariant(item.pembayaran.status_pembayaran)">
                      {{ getPaymentLabel(item.pembayaran.status_pembayaran) }}
                    </Badge>
                    <Badge v-else variant="secondary">
                      Belum Bayar
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(item.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin-daerah.pendaftaran.show', item.id_pendaftaran))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin-daerah.pendaftaran.edit', item.id_pendaftaran))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          v-if="item.status_pendaftaran === 'draft'"
                          @click="submitPendaftaran(item.id_pendaftaran)"
                        >
                          <Icon name="send" class="w-4 h-4 mr-2" />
                          Submit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          @click="$inertia.visit(route('admin-daerah.pendaftaran.documents', item.id_pendaftaran))"
                        >
                          <Icon name="file-text" class="w-4 h-4 mr-2" />
                          Dokumen
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          v-if="item.status_pendaftaran !== 'approved'"
                          @click="deletePendaftaran(item.id_pendaftaran)"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-700">
            Menampilkan {{ pendaftaran.from }} - {{ pendaftaran.to }} dari {{ pendaftaran.total }} data
          </span>
        </div>
        <div class="flex items-center space-x-2">
          <Button
            v-for="link in pendaftaran.links"
            :key="link.label"
            :variant="link.active ? 'default' : 'outline'"
            :disabled="!link.url"
            @click="link.url && $inertia.visit(link.url)"
            size="sm"
            v-html="link.label"
          />
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Avatar from '@/components/ui/avatar/Avatar.vue'
import AvatarFallback from '@/components/ui/avatar/AvatarFallback.vue'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'

interface Props {
  pendaftaran: any
  golongan: any[]
}

const props = defineProps<Props>()

const filters = reactive({
  search: '',
  status: 'all',
  golongan: 'all'
})

const search = () => {
  const params = Object.fromEntries(
    Object.entries(filters).filter(([_, value]) => value !== '' && value !== 'all')
  )
  
  router.get(route('admin-daerah.pendaftaran.index'), params, {
    preserveState: true,
    replace: true
  })
}

const resetFilters = () => {
  filters.search = ''
  filters.status = 'all'
  filters.golongan = 'all'
  search()
}

const getInitials = (name: string) => {
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
}

const getStatusVariant = (status: string) => {
  const variants = {
    'draft': 'secondary',
    'submitted': 'default',
    'verified': 'default',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels = {
    'draft': 'Draft',
    'submitted': 'Disubmit',
    'verified': 'Diverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const getPaymentVariant = (status: string) => {
  const variants = {
    'pending': 'secondary',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getPaymentLabel = (status: string) => {
  const labels = {
    'pending': 'Pending',
    'approved': 'Lunas',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}

const submitPendaftaran = (id: number) => {
  if (confirm('Yakin ingin submit pendaftaran ini?')) {
    router.post(route('admin-daerah.pendaftaran.submit', id))
  }
}

const deletePendaftaran = (id: number) => {
  if (confirm('Yakin ingin menghapus pendaftaran ini?')) {
    router.delete(route('admin-daerah.pendaftaran.destroy', id))
  }
}
</script>
